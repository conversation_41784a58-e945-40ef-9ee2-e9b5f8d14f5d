import numpy as np
from sklearn.base import BaseEstimator, ClassifierMixin
from sklearn.utils.validation import check_X_y, check_array, check_is_fitted
from sklearn.utils.multiclass import unique_labels
from sklearn.linear_model import LogisticRegression

class SoftmaxClassifier(BaseEstimator, ClassifierMixin):
    """
    A classifier that uses softmax for classification.
    This is designed to be a drop-in replacement for RandomForestClassifier
    while maintaining the same prediction behavior.

    Parameters
    ----------
    random_state : int, default=None
        Controls the randomness of the estimator.
    """

    def __init__(self, random_state=None):
        self.random_state = random_state
        # We'll use LogisticRegression with 'multinomial' solver as our base classifier
        # This implements softmax regression
        self.model = LogisticRegression(
            multi_class='multinomial',
            solver='lbfgs',
            C=10.0,  # Increased regularization parameter for better generalization
            max_iter=2000,  # Increased max iterations for better convergence
            class_weight='balanced',  # Use balanced class weights to handle class imbalance
            random_state=random_state
        )

    def fit(self, X, y):
        """
        Fit the softmax classifier.

        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            The training input samples.
        y : array-like of shape (n_samples,)
            The target values.

        Returns
        -------
        self : object
            Returns self.
        """
        # Check that X and y have correct shape
        X, y = check_X_y(X, y)
        # Store the classes seen during fit
        self.classes_ = unique_labels(y)

        # Train the model
        self.model.fit(X, y)

        # Save the training data for predict_proba
        self.X_ = X
        self.y_ = y

        # Return the classifier
        return self

    def predict_proba(self, X):
        """
        Return probability estimates for the test data X.

        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            The input samples.

        Returns
        -------
        p : ndarray of shape (n_samples, n_classes)
            The class probabilities of the input samples.
        """
        # Check is fit had been called
        check_is_fitted(self, ['X_', 'y_'])

        # Input validation
        X = check_array(X)

        # Get softmax probabilities from the model
        proba = self.model.predict_proba(X)

        return proba

    def predict(self, X):
        """
        Predict class for X.

        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            The input samples.

        Returns
        -------
        y : ndarray of shape (n_samples,)
            The predicted classes.
        """
        # Check is fit had been called
        check_is_fitted(self, ['X_', 'y_'])

        # Input validation
        X = check_array(X)

        # Get predictions from the model
        return self.model.predict(X)

    def score(self, X, y):
        """
        Return the mean accuracy on the given test data and labels.

        Parameters
        ----------
        X : array-like of shape (n_samples, n_features)
            Test samples.
        y : array-like of shape (n_samples,)
            True labels for X.

        Returns
        -------
        score : float
            Mean accuracy of self.predict(X) wrt. y.
        """
        return self.model.score(X, y)
