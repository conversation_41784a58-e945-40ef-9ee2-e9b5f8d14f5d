import os
import cv2
import numpy as np
import pickle
from model import load_or_create_model, extract_features, classify_image, enable_vgg16_features

def test_softmax_classifier():
    """Test the softmax classifier and ensure it produces the expected predictions"""
    print("Testing softmax classifier...")
    
    # Load the model
    print("Loading model...")
    model = load_or_create_model()
    
    # Get test images
    test_dir = 'DS_COW/train'
    categories = os.listdir(test_dir)
    test_images = []
    
    # Get one image from each category
    for category in categories:
        category_dir = os.path.join(test_dir, category)
        if os.path.isdir(category_dir):
            images = os.listdir(category_dir)
            if images:
                img_path = os.path.join(category_dir, images[0])
                test_images.append((img_path, category))
    
    # Test with softmax classifier
    print("\n=== Testing with softmax classifier ===")
    
    for img_path, expected_category in test_images:
        print(f"\nTesting with image: {img_path}")
        
        # Read the image
        img = cv2.imread(img_path)
        if img is None:
            print(f"Error: Could not read image {img_path}")
            continue
        
        # Get RGB version for display
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Extract features
        features = extract_features(img_rgb, model)
        
        # Make prediction
        condition_probs, predicted_condition = classify_image(features, model)
        
        # Print results
        print(f"Predicted condition: {predicted_condition}")
        print(f"Probabilities:")
        for condition, prob in sorted(condition_probs.items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f"  {condition}: {prob:.4f}")
        
        # Check if prediction matches expected category
        expected_display = expected_category.replace('_', ' ').title()
        if predicted_condition == expected_display:
            print(f"✓ CORRECT: Prediction matches expected category '{expected_category}'")
        else:
            print(f"✗ INCORRECT: Prediction '{predicted_condition}' does not match expected category '{expected_category}'")
    
    # Test with VGG16 features
    print("\n=== Enabling VGG16 features ===")
    model = enable_vgg16_features(model, True)
    
    print("\n=== Testing with VGG16 features ===")
    
    for img_path, expected_category in test_images:
        print(f"\nTesting with image: {img_path}")
        
        # Read the image
        img = cv2.imread(img_path)
        if img is None:
            print(f"Error: Could not read image {img_path}")
            continue
        
        # Get RGB version for display
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Extract features
        features = extract_features(img_rgb, model)
        
        # Make prediction
        condition_probs, predicted_condition = classify_image(features, model)
        
        # Print results
        print(f"Predicted condition: {predicted_condition}")
        print(f"Probabilities:")
        for condition, prob in sorted(condition_probs.items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f"  {condition}: {prob:.4f}")
        
        # Check if prediction matches expected category
        expected_display = expected_category.replace('_', ' ').title()
        if predicted_condition == expected_display:
            print(f"✓ CORRECT: Prediction matches expected category '{expected_category}'")
        else:
            print(f"✗ INCORRECT: Prediction '{predicted_condition}' does not match expected category '{expected_category}'")
    
    # Disable VGG16 features to restore original behavior
    print("\n=== Disabling VGG16 features ===")
    model = enable_vgg16_features(model, False)
    
    return True

if __name__ == "__main__":
    test_softmax_classifier()
