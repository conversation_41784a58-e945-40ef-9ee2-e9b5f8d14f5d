import os
import sys

def rename_images(folder_path, temp_prefix="temp_", final_prefix="image_"):
    image_files = [f for f in os.listdir(folder_path)
                   if os.path.isfile(os.path.join(folder_path, f)) and
                   f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.gif'))]
    
    if not image_files:
        print("No image files found in the folder.")
        return

    image_files.sort()
    temp_names = []

    for i, filename in enumerate(image_files, start=1):
        ext = os.path.splitext(filename)[1]
        temp_name = f"{temp_prefix}{i}{ext}"
        os.rename(os.path.join(folder_path, filename), os.path.join(folder_path, temp_name))
        temp_names.append(temp_name)

    for i, temp_name in enumerate(temp_names, start=1):
        ext = os.path.splitext(temp_name)[1]
        final_name = f"{final_prefix}{i}{ext}"
        os.rename(os.path.join(folder_path, temp_name), os.path.join(folder_path, final_name))

    print("Renaming complete.")

if __name__ == "__main__":
    # Default folder path if no argument is given
    default_path = r"D:\UdderHealthAI final 1\UdderHealthAI\UdderHealthAI\UdderHealthAI\DS_COW\valid\healthy"

    if len(sys.argv) == 2:
        rename_images(sys.argv[1])
    else:
        print(f"No folder argument given, using default:\n{default_path}")
        rename_images(default_path)
