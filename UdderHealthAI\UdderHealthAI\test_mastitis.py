import cv2
import numpy as np
from model import load_or_create_model, extract_features, classify_image

# Test with all mastitis images
def test_mastitis_images():
    # Load the model
    model = load_or_create_model()
    
    # Get all mastitis images
    import os
    mastitis_dir = "DS_COW/train/mastitis"
    images = [f for f in os.listdir(mastitis_dir) if f.endswith('.jpg')]
    
    correct_count = 0
    total_count = len(images)
    
    print(f"Testing {total_count} mastitis images...")
    
    for img_file in images:
        img_path = os.path.join(mastitis_dir, img_file)
        img = cv2.imread(img_path)
        if img is None:
            print(f"Error loading image: {img_path}")
            continue
            
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Extract features
        features = extract_features(img_rgb, model)
        
        # Classify
        probs, pred = classify_image(features, model)
        
        # Check if correctly classified
        is_correct = pred == "Mastitis"
        status = "✓" if is_correct else "✗"
        
        if is_correct:
            correct_count += 1
            
        print(f"{status} {img_file}: Predicted as {pred}")
        
        # Print top 3 probabilities
        top_probs = sorted(probs.items(), key=lambda x: x[1], reverse=True)[:3]
        for condition, prob in top_probs:
            print(f"  {condition}: {prob:.4f}")
    
    accuracy = correct_count / total_count * 100
    print(f"\nMastitis detection accuracy: {accuracy:.2f}% ({correct_count}/{total_count})")
    
    return accuracy, correct_count, total_count

if __name__ == "__main__":
    test_mastitis_images()