Starting softmax model training with VGG16 features enabled...
Training for 3 epochs
Loading training and validation data...
Loading healthy training images...
Loading healthy validation images...
Loading frozen_teats training images...
Loading frozen_teats validation images...
Loading mastitis training images...
Loading mastitis validation images...
Loading teat_lesions training images...
Loading teat_lesions validation images...
Loading low_udder_score training images...
Loading low_udder_score validation images...
Loading medium_udder_score training images...
Loading medium_udder_score validation images...
Loading high_udder_score training images...
Loading high_udder_score validation images...
Training data shape: (3425, 224, 224, 3), Labels shape: (3425,)
Validation data shape: (435, 224, 224, 3), Labels shape: (435,)
Preparing features for training...
Extracting features with VGG16...
Feature vector shape: (2740, 54)
Normalizing features...
Training the softmax model...

Epoch 1/3
  Train Accuracy: 0.9854
  Validation Accuracy: 0.9632
  Precision: 0.9635
  Recall: 0.9632
  F1 Score: 0.9631

Epoch 2/3
  Train Accuracy: 0.9927
  Validation Accuracy: 0.9701
  Precision: 0.9705
  Recall: 0.9701
  F1 Score: 0.9702

Epoch 3/3
  Train Accuracy: 0.9964
  Validation Accuracy: 0.9747
  Precision: 0.9751
  Recall: 0.9747
  F1 Score: 0.9748

Model training completed in 187.45 seconds

Final Model Performance:
  Training Accuracy: 0.9964
  Validation Accuracy: 0.9747
  Test Accuracy: 0.9765
  Test Precision: 0.9768
  Test Recall: 0.9765
  Test F1 Score: 0.9766

Training plots saved to 'training_plots' directory
Saving the scaler...
Saving the softmax model...
Softmax model training complete!
Final training accuracy: 0.9964
Final validation accuracy: 0.9747
Final test accuracy: 0.9765
