import os
import numpy as np
import cv2
from sklearn.cluster import KM<PERSON><PERSON>
import shutil
from sklearn.model_selection import train_test_split
import random
import matplotlib.pyplot as plt

# Define our categories
categories = [
    'healthy',
    'frozen_teats',
    'mastitis',
    'teat_lesions',
    'low_udder_score',
    'medium_udder_score',
    'high_udder_score'
]

# Create necessary folders
def create_folders():
    # Create main category folders
    for category in categories:
        os.makedirs(os.path.join('DS_COW', category), exist_ok=True)
    
    # Create train, test, valid folders
    os.makedirs(os.path.join('DS_COW', 'train'), exist_ok=True)
    os.makedirs(os.path.join('DS_COW', 'test'), exist_ok=True)
    os.makedirs(os.path.join('DS_COW', 'valid'), exist_ok=True)
    
    # Create category folders inside train, test, valid
    for split in ['train', 'test', 'valid']:
        for category in categories:
            os.makedirs(os.path.join('DS_COW', split, category), exist_ok=True)

# Function to extract features from an image
def extract_features(image_path):
    try:
        # Read image
        img = cv2.imread(image_path)
        if img is None:
            return None
        
        # Resize for consistency
        img = cv2.resize(img, (224, 224))
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Extract histogram features
        hist = cv2.calcHist([gray], [0], None, [32], [0, 256])
        hist = cv2.normalize(hist, hist).flatten()
        
        # Extract color features
        color_features = []
        for i in range(3):  # BGR channels
            hist = cv2.calcHist([img], [i], None, [32], [0, 256])
            hist = cv2.normalize(hist, hist).flatten()
            color_features.extend(hist)
        
        # Extract texture features using Haralick
        gray = cv2.resize(gray, (64, 64))  # Resize for faster computation
        texture_features = []
        
        # Calculate GLCM and extract Haralick features
        glcm = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        texture_features = np.mean(glcm), np.std(glcm), np.max(glcm)
        
        # Combine all features
        features = np.concatenate((hist, color_features, texture_features))
        return features
    
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return None

# Classify images using K-means clustering
def classify_images():
    # Get all image paths
    image_files = [f for f in os.listdir('DS_COW') if f.endswith('.jpg')]
    image_paths = [os.path.join('DS_COW', f) for f in image_files]
    
    # Extract features for all images
    print("Extracting features from images...")
    features_list = []
    valid_image_paths = []
    
    for path in image_paths:
        features = extract_features(path)
        if features is not None:
            features_list.append(features)
            valid_image_paths.append(path)
    
    if not features_list:
        print("No valid features extracted. Check images.")
        return
    
    # Convert to numpy array
    features_array = np.array(features_list)
    
    # Normalize features
    features_mean = np.mean(features_array, axis=0)
    features_std = np.std(features_array, axis=0)
    features_std[features_std == 0] = 1  # Avoid division by zero
    features_normalized = (features_array - features_mean) / features_std
    
    # Perform K-means clustering
    print(f"Clustering images into {len(categories)} categories...")
    kmeans = KMeans(n_clusters=len(categories), random_state=42, n_init=10)
    cluster_labels = kmeans.fit_predict(features_normalized)
    
    # Map clusters to categories
    print("Organizing images into category folders...")
    for i, path in enumerate(valid_image_paths):
        cluster = cluster_labels[i]
        category = categories[cluster]
        
        # Copy file to category folder
        filename = os.path.basename(path)
        shutil.copy(path, os.path.join('DS_COW', category, filename))
        print(f"Classified {filename} as {category}")

# Split data into train/test/valid
def split_data():
    print("Splitting data into train/test/valid sets (80:10:10)...")
    
    for category in categories:
        category_dir = os.path.join('DS_COW', category)
        if not os.path.exists(category_dir):
            print(f"Warning: Category folder {category} does not exist or is empty.")
            continue
        
        # Get all images in this category
        images = [f for f in os.listdir(category_dir) if f.endswith('.jpg')]
        
        if not images:
            print(f"Warning: No images found in category {category}.")
            continue
        
        # Handle case with very few images
        num_images = len(images)
        
        if num_images <= 3:
            # If we have 3 or fewer images, just put them all in training
            train_images = images
            test_images = []
            valid_images = []
            print(f"Warning: Category {category} has only {num_images} images, all assigned to training.")
        elif num_images <= 5:
            # If we have 4-5 images, do 3:1:1 split
            train_images = images[0:max(1, num_images-2)]
            test_images = images[len(train_images):len(train_images)+1] if len(train_images) < num_images else []
            valid_images = images[len(train_images)+len(test_images):] if len(train_images)+len(test_images) < num_images else []
        else:
            # Normal case - shuffle and split
            random.shuffle(images)
            
            # Calculate split points for 80:10:10
            train_size = int(0.8 * num_images)
            test_size = int(0.1 * num_images)
            
            # Ensure we have at least one image in each set if possible
            train_size = max(1, min(train_size, num_images-2))
            test_size = max(1, min(test_size, num_images-train_size-1))
            
            # Split the data
            train_images = images[:train_size]
            test_images = images[train_size:train_size+test_size]
            valid_images = images[train_size+test_size:]
        
        # Copy images to respective folders
        for img in train_images:
            shutil.copy(
                os.path.join(category_dir, img),
                os.path.join('DS_COW', 'train', category, img)
            )
        
        for img in test_images:
            shutil.copy(
                os.path.join(category_dir, img),
                os.path.join('DS_COW', 'test', category, img)
            )
        
        for img in valid_images:
            shutil.copy(
                os.path.join(category_dir, img),
                os.path.join('DS_COW', 'valid', category, img)
            )
        
        print(f"{category}: {len(train_images)} train, {len(test_images)} test, {len(valid_images)} valid")

# Run the organization process
if __name__ == "__main__":
    print("Creating folders...")
    create_folders()
    
    print("Classifying images...")
    classify_images()
    
    print("Splitting data...")
    split_data()
    
    print("Dataset organization complete!")