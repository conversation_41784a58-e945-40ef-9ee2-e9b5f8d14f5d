import os
import cv2
import numpy as np
import pickle
import matplotlib.pyplot as plt
from model import load_or_create_model, extract_features, classify_image, enable_vgg16_features

def test_vgg16_feature_extraction():
    """Test VGG16 feature extraction and ensure it doesn't affect predictions"""
    print("Testing VGG16 feature extraction...")
    
    # Load the model
    print("Loading model...")
    model = load_or_create_model()
    
    # Get test images
    test_dir = 'DS_COW/train'
    categories = os.listdir(test_dir)
    test_images = []
    
    # Get one image from each category
    for category in categories:
        category_dir = os.path.join(test_dir, category)
        if os.path.isdir(category_dir):
            images = os.listdir(category_dir)
            if images:
                img_path = os.path.join(category_dir, images[0])
                test_images.append((img_path, category))
    
    # Test without VGG16 features
    print("\n=== Testing with original features ===")
    original_results = []
    
    for img_path, expected_category in test_images:
        print(f"\nTesting with image: {img_path}")
        
        # Read the image
        img = cv2.imread(img_path)
        if img is None:
            print(f"Error: Could not read image {img_path}")
            continue
        
        # Get RGB version for display
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Extract features
        features = extract_features(img_rgb, model)
        
        # Make prediction
        condition_probs, predicted_condition = classify_image(features, model)
        
        # Print results
        print(f"Predicted condition: {predicted_condition}")
        print(f"Probabilities:")
        for condition, prob in sorted(condition_probs.items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f"  {condition}: {prob:.4f}")
        
        # Store results
        original_results.append((img_path, predicted_condition, condition_probs))
    
    # Enable VGG16 features
    print("\n=== Enabling VGG16 features ===")
    model = enable_vgg16_features(model, True)
    
    # Test with VGG16 features
    print("\n=== Testing with VGG16 features ===")
    vgg16_results = []
    
    for img_path, expected_category in test_images:
        print(f"\nTesting with image: {img_path}")
        
        # Read the image
        img = cv2.imread(img_path)
        if img is None:
            print(f"Error: Could not read image {img_path}")
            continue
        
        # Get RGB version for display
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # Extract features
        features = extract_features(img_rgb, model)
        
        # Make prediction
        condition_probs, predicted_condition = classify_image(features, model)
        
        # Print results
        print(f"Predicted condition: {predicted_condition}")
        print(f"Probabilities:")
        for condition, prob in sorted(condition_probs.items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f"  {condition}: {prob:.4f}")
        
        # Store results
        vgg16_results.append((img_path, predicted_condition, condition_probs))
    
    # Compare results
    print("\n=== Comparing results ===")
    all_match = True
    
    for i, ((img_path1, pred1, _), (img_path2, pred2, _)) in enumerate(zip(original_results, vgg16_results)):
        if pred1 != pred2:
            print(f"Mismatch for {img_path1}:")
            print(f"  Original prediction: {pred1}")
            print(f"  VGG16 prediction: {pred2}")
            all_match = False
    
    if all_match:
        print("All predictions match! VGG16 feature extraction doesn't affect the prediction results.")
    
    # Disable VGG16 features to restore original behavior
    print("\n=== Disabling VGG16 features ===")
    model = enable_vgg16_features(model, False)
    
    return all_match

if __name__ == "__main__":
    test_vgg16_feature_extraction()
